[VideoCompression] Simple compression result: file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4
TipTubeUploadScreen.tsx:461 [TipTubeUpload] Video compression result: {originalUri: 'file:///data/user/0/com.adtip.app.adtip_app/cache/rn_image_picker_lib_temp_e1ea3f75-f478-49ec-bc48-d1e3352717d4.mp4', compressedUri: 'file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4', originalSize: 1967284, compressedSize: 961104, compressionRatio: 2.046900231400556, success: true, error: undefined}
TipTubeUploadScreen.tsx:478 [TipTubeUpload] Compressed video file stats: {path: 'file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4', size: 961104, exists: true, expectedSize: 961104, sizesMatch: true}
TipTubeUploadScreen.tsx:673 [TipTubeUpload] Step 2: Uploading media files
TipTubeUploadScreen.tsx:500 [TipTubeUpload] Starting unified upload (Stream/R2)
TipTubeUploadScreen.tsx:527 [TipTubeUpload] User info for upload: {userId: '58422', userName: 'R17 C', channelId: '11533'}
UploadConfig.ts:64 [UploadConfig] Checking Stream upload eligibility for tiptube
UploadConfig.ts:65 [UploadConfig] Current config: {useStreamUploads: true, streamUploadPercentage: 100, preferStreamForTipShorts: true, preferStreamForTipTube: true, preferStreamForCampaigns: true}
UploadConfig.ts:81 [UploadConfig] Random percentage: 42.35492207314707, threshold: 100
UploadConfig.ts:104 [UploadConfig] Content preference for tiptube: true
UploadConfig.ts:105 [UploadConfig] Final decision for tiptube: Stream
UnifiedUploadService.ts:151 [UnifiedUpload] TipTube upload starting with config: {useStreamUploads: true, streamUploadPercentage: 100, preferStreamForTipTube: true, enableR2Fallback: true, selectedMethod: 'stream'}
UploadConfig.ts:183 [UploadConfig] tiptube upload method: stream (Config: 100% rollout)
TipTubeUploadScreen.tsx:538 [TipTubeUpload] Stream upload: Determining upload method... (0%)
UnifiedUploadService.ts:172 [UnifiedUpload] Attempting Stream upload for TipTube
UnifiedUploadService.ts:272 [UnifiedUpload] Starting Stream upload
UnifiedUploadService.ts:273 [UnifiedUpload] Upload data: {videoUri: 'file:///data/user/0/com.adtip.app.adtip_app/cache/...', thumbnailUri: 'file:///data/user/0/com.adtip.app.adtip_app/cache/...', metadata: {…}}
UnifiedUploadService.ts:279 [UnifiedUpload] User info provided: {userId: '58422', userName: 'R17 C', channelId: '11533'}
UnifiedUploadService.ts:292 [UnifiedUpload] Video file prepared: {uri: 'file:///data/user/0/com.adtip.app.adtip_app/cache/...', type: 'video/mp4', name: 'He_he_he_1753518301120.mp4'}
UnifiedUploadService.ts:298 [UnifiedUpload] Starting DirectUploadService.completeUploadWorkflow...
UnifiedUploadService.ts:310 [UnifiedUpload] Transformed metadata: {name: 'He he he', description: 'Eh eh rn rh rn', categoryId: 1, createdBy: 58422, videoChannel: '11533', isShot: false}
DirectUploadService.ts:311 [DirectUpload] Starting complete upload workflow
DirectUploadService.ts:312 [DirectUpload] Video metadata: {name: 'He he he', description: 'Eh eh rn rh rn', categoryId: 1, createdBy: 58422, videoChannel: '11533', isShot: false}
DirectUploadService.ts:313 [DirectUpload] User info: {userId: '58422', userName: 'R17 C', channelId: '11533'}
UnifiedUploadService.ts:317 [UnifiedUpload] Stream upload progress: Creating upload URL... - undefined%
TipTubeUploadScreen.tsx:538 [TipTubeUpload] Stream upload: Creating upload URL... (0%)
DirectUploadService.ts:317 [DirectUpload] Step 1: Creating upload URL...
DirectUploadService.ts:136 [DirectUpload] Creating TipTube upload URL for: {userId: '58422', userName: 'R17 C', channelId: '11533'}
DirectUploadService.ts:137 [DirectUpload] API URL: http://*************:7082/api/direct-upload/tiptube
DirectUploadService.ts:149 [DirectUpload] TipTube upload URL response: {status: 400, data: {…}}
DirectUploadService.ts:155 [DirectUpload] Failed to create TipTube upload URL: Authentication error
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ DirectUploadService.ts:155
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 8 more frames
Show less
DirectUploadService.ts:323 [DirectUpload] Upload URL creation response: {success: false, error: 'Authentication error', hasData: false}error: "Authentication error"hasData: falsesuccess: false[[Prototype]]: Object
DirectUploadService.ts:330 [DirectUpload] Failed to create upload URL: Authentication error
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ DirectUploadService.ts:330
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 8 more frames
Show less
UnifiedUploadService.ts:326 [UnifiedUpload] DirectUploadService.completeUploadWorkflow completed: {success: false, videoId: undefined, error: 'Authentication error'}error: "Authentication error"success: falsevideoId: undefined[[Prototype]]: Object
UnifiedUploadService.ts:343 [UnifiedUpload] Stream upload failed with error: Authentication error
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ UnifiedUploadService.ts:343
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 8 more frames
Show less
UnifiedUploadService.ts:344 [UnifiedUpload] Full Stream upload result: {success: false, error: 'Authentication error'}error: "Authentication error"success: false[[Prototype]]: Object
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ UnifiedUploadService.ts:344
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 8 more frames
Show less
UnifiedUploadService.ts:175 [UnifiedUpload] Stream upload result: {success: false, error: 'Authentication error', method: 'stream'}error: "Authentication error"method: "stream"success: false[[Prototype]]: Object
UnifiedUploadService.ts:183 [UnifiedUpload] Stream upload failed, falling back to R2
UnifiedUploadService.ts:184 [UnifiedUpload] Stream failure details: Authentication error
UploadConfig.ts:183 [UploadConfig] tiptube upload method: r2 (Stream fallback)
UnifiedUploadService.ts:365 [UnifiedUpload] Starting R2 upload
TipTubeUploadScreen.tsx:540 [TipTubeUpload] R2 upload: Uploading to R2... (0%)
CloudflareUploadService.ts:575 [CloudflareUpload] Starting TipTube batch upload {videoPath: 'file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4', thumbnailPath: 'file:///data/user/0/com.adtip.app.adtip_app/cache/rn_image_picker_lib_temp_c232b352-b7d0-4b6b-b0c3-164a61f06d58.jpg', userId: 58422}
TipTubeUploadScreen.tsx:540 [TipTubeUpload] R2 upload: Uploading to R2... (0%)
CloudflareUploadService.ts:584 [CloudflareUpload] Uploading TipTube video to /videos...
CloudflareUploadService.ts:236 [CloudflareUpload] Starting upload: file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4
CloudflareUploadService.ts:262 [CloudflareUpload] Checking file before upload: file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4
CloudflareUploadService.ts:269 [CloudflareUpload] File info from RNFS.stat: {path: 'file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4', size: 961104, isFile: true, isDirectory: false, mtime: {…}, ctime: {…}}
CloudflareUploadService.ts:297 [CloudflareUpload] Reading file data, isVideo: true fileSize: 961104
CloudflareUploadService.ts:161 [CloudflareUpload] Using direct file URI for video upload: file:///data/user/0/com.adtip.app.adtip_app/cache/9a694cca-7eea-421f-92b4-53c9fbca9ee6.mp4
TipTubeUploadScreen.tsx:540 [TipTubeUpload] R2 upload: Uploading to R2... (0%)
CloudflareUploadService.ts:306 [CloudflareUpload] Preparing upload command: {bucket: 'adtip', key: 'videos/1753518303678_vu6ws7.mp4', contentType: 'video/mp4', dataSize: 961104, originalFileSize: 961104, isVideo: true, originalName: 'tiptube_1753518303634.mp4'}
CloudflareUploadService.ts:332 [CloudflareUpload] Sending upload command to R2...
CloudflareUploadService.ts:334 [CloudflareUpload] R2 response: {$metadata: {…}, ETag: '"83842353aa7fd8d8b4ce18872ffd92a4"', ChecksumCRC32: 'GBcH3Q==', VersionId: '7e67ba2a072999b45980175b025232ce'}
TipTubeUploadScreen.tsx:540 [TipTubeUpload] R2 upload: Uploading to R2... (70%)
CloudflareUploadService.ts:344 [CloudflareUpload] Upload successful: {key: 'videos/1753518303678_vu6ws7.mp4', size: 961104, contentType: 'video/mp4', url: 'https://theadtip.in/videos/1753518303678_vu6ws7.mp4'}
CloudflareUploadService.ts:605 [CloudflareUpload] TipTube video upload successful: https://theadtip.in/videos/1753518303678_vu6ws7.mp4
CloudflareUploadService.ts:609 [CloudflareUpload] Uploading TipTube thumbnail to /images...